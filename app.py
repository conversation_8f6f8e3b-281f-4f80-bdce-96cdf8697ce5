import datetime
import os
from dotenv import load_dotenv

load_dotenv(override=True)
import re
from urllib.parse import urlparse
from flask import (
    Flask,
    flash,
    jsonify,
    render_template,
    render_template_string,
    request,
    redirect,
    url_for,
)
from werkzeug.utils import secure_filename
import psycopg2
import boto3
from boto3.session import Session
from botocore.client import Config
import random
from pypdf import Pdf<PERSON>eader
import pytesseract
from PIL import Image
import io
import pdf2image
import redmail
import json
import feedgenerator
import pycountry
import uuid

# Import new modules
from custom_libs.models import (
    db, Vacancy, Employer, Candidate, Application, TalentPool, JobAlert,
    HighlightedJobs, VacanciesView, AllJobLocations, EmployerCards
)
from custom_libs.rss_manager import rss_manager
from custom_libs.notification_system import notification_system
from custom_libs.scheduler import job_scheduler
from custom_libs.personal_data_cencorship import UnicodeAwareCVCensor
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate


# Initialize S3 client
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_REGION_NAME = os.getenv("AWS_REGION_NAME", "fra1")
AWS_ENDPOINT_URL = os.getenv("AWS_ENDPOINT_URL", "https://fra1.digitaloceanspaces.com")

PG_HOST = os.getenv("PGHOST")
PG_DATABASE = os.getenv("PGDATABASE")
PG_USER = os.getenv("PGUSER")
PG_PASSWORD = os.getenv("PGPASSWORD")
DATABASE_URL = os.getenv("DATABASE_URL")

# Configure SQLAlchemy
app = Flask(__name__)
app.secret_key = "super secret key"

# Ensure DATABASE_URL is set
if not DATABASE_URL:
    raise RuntimeError("DATABASE_URL environment variable must be set")

app.config['SQLALCHEMY_DATABASE_URI'] = DATABASE_URL
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize S3 client with proper error handling
try:
    if not AWS_ACCESS_KEY_ID or not AWS_SECRET_ACCESS_KEY:
        raise ValueError("AWS credentials not found in environment variables")

    s3_session = boto3.session.Session()
    client = s3_session.client(
        "s3",
        region_name=AWS_REGION_NAME,
        endpoint_url=AWS_ENDPOINT_URL,
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    )
    print("S3 client initialized successfully", flush=True)
except Exception as e:
    print(f"Error initializing S3 client: {str(e)}", flush=True)
    client = None

# Initialize SQLAlchemy and extensions
db.init_app(app)
migrate = Migrate(app, db)

# Initialize custom systems
rss_manager.init_app(app)
notification_system.init_app(app)
job_scheduler.init_app(app)

# Start background scheduler
with app.app_context():
    job_scheduler.start()

ALLOWED_EXTENSIONS = {"pdf"}
MAX_FILE_SIZE = 3 * 1024 * 1024  # 3MB

# Phone number validation pattern
PHONE_REGEX = re.compile(r"^\+?[1-9]\d{1,14}$")  # E.164 format

# Email validation pattern - more robust than basic regex
EMAIL_REGEX = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")

# Common fake email domains to block
FAKE_EMAIL_DOMAINS = {
    "example.com", "test.com", "fake.com", "invalid.com", "dummy.com",
    "temp.com", "temporary.com", "disposable.com", "throwaway.com",
    "mailinator.com", "10minutemail.com", "guerrillamail.com"
}

def is_valid_email(email):
    """
    Validate email address with basic checks to prevent fake/bot emails
    """
    if not email or not isinstance(email, str):
        return False

    email = email.strip().lower()

    # Basic format validation
    if not EMAIL_REGEX.match(email):
        return False

    # Check for fake domains
    domain = email.split('@')[1] if '@' in email else ''
    if domain in FAKE_EMAIL_DOMAINS:
        return False

    # Check for suspicious patterns
    if email.count('@') != 1:
        return False

    # Check for minimum length
    if len(email) < 5:
        return False

    return True


# Helper function for file validation
def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


@app.errorhandler(404)
def page_not_found(e):
    return render_template("404.html"), 404



def send_app_confirmation_email(
    recipient,
    fname,
    vacancy_id,
    application_id,
    employer_id,
    employer_name,
    vacancy_title,
):
    print("Sending email", flush=True)
    # email_subject_key = f"#V{vacancy_id}E{employer_id}A{application_id}"
    email_subject_key = f"#A{application_id}"
    subject = f"Application Confirmation for {vacancy_title} at {employer_name} - {email_subject_key}"
    body = f"Dear {fname},\n\n Thank you for applying for the {vacancy_title} position at {employer_name}.\n\n We will review your application and get back to you soon.\n\nRegards,\n {employer_name}"
    sender = os.getenv("MAIL_USERNAME")

    # Send email using redmail with improved error handling
    try:
        # Validate email configuration
        smtp_host = os.getenv("SMTP_MAIL_HOST")
        smtp_port = int(os.getenv("SMTP_MAIL_PORT", 587))
        mail_password = os.getenv("MAIL_PASSWORD")

        if not all([smtp_host, sender, mail_password]):
            raise ValueError("Email configuration incomplete")

        r_email = redmail.EmailSender(
            host=smtp_host,
            port=smtp_port,
            username=sender,
            password=mail_password,
        )

        # Establish connection first
        r_email.connect()

        # Send the email
        r_email.send(
            subject=f"{subject} - {email_subject_key}",
            sender=f"{employer_name} <{sender}>",
            text=body,
            receivers=recipient,
        )

        # Close connection
        r_email.close()
        print("Email sent successfully", flush=True)

    except Exception as e:
        print(f"Error sending email: {str(e)}", flush=True)
        # Log more details for debugging
        print(f"Email config - Host: {os.getenv('SMTP_MAIL_HOST')}, Port: {os.getenv('SMTP_MAIL_PORT')}, Username: {sender}", flush=True)


@app.route("/")
def index():
    """
    Homepage route - shows recent jobs from both database and RSS feeds.
    Combines all sources to show at least 16 jobs.
    """
    try:
        # Try to use the database connection safely
        conn, cur = ats_safe_connect()

        # Get recent database jobs
        database_jobs = []
        try:
            cur.execute(
                """SELECT
                *, 'Database' as listing_source
                FROM highlighted_jobs
                WHERE vacancy_status = 'Active'
                ORDER BY vacancy_creation_date DESC
                LIMIT 12;"""
            )
            columns = [col[0] for col in cur.description]
            database_jobs = [dict(zip(columns, row)) for row in cur.fetchall()]
        except Exception as e:
            app.logger.warning(f"Could not fetch database jobs: {str(e)}")
            database_jobs = []

        cur.close()

        # Get RSS feed jobs
        rss_jobs = []
        try:
            rss_jobs_raw = rss_manager.get_cached_vacancies(limit=12)
            # Add listing_source to RSS jobs
            for job in rss_jobs_raw:
                job['listing_source'] = 'RSS'
            rss_jobs = rss_jobs_raw
        except Exception as e:
            app.logger.warning(f"Could not fetch RSS jobs: {str(e)}")
            rss_jobs = []

        # Combine and sort all jobs by creation date
        all_jobs = database_jobs + rss_jobs

        # Sort by creation date (newest first) and take top 20
        try:
            all_jobs.sort(key=lambda x: x.get('vacancy_creation_date', ''), reverse=True)
        except:
            # If sorting fails, just use the order we have
            pass

        # Take the first 20 jobs to ensure we have enough
        all_recent_jobs = all_jobs[:20]

        # Get total counts for stats
        total_jobs = len(all_jobs) if all_jobs else 0
        total_companies = len(set(job.get('employer_name', '') for job in all_jobs if job.get('employer_name'))) if all_jobs else 0

        # Ensure minimum values for display
        total_jobs = max(total_jobs + rss_manager.get_number_of_cached_jobs(), 50)  # Show at least 50+ for marketing
        total_companies = max(total_companies, 20)  # Show at least 20+ for marketing

        return render_template(
            "index.html",
            all_recent_jobs=all_recent_jobs,
            total_jobs=total_jobs,
            total_companies=total_companies,
        )
    except Exception as e:
        app.logger.error(f"Error in index route: {str(e)}")
        # Fallback to minimal data
        return render_template(
            "index.html",
            all_recent_jobs=[],
            total_jobs=0,
            total_companies=0,
        )


@app.route("/employer-signup", methods=["GET", "POST"])
def employer_signup():
    if request.method == "POST":
        # Get form data
        company_name = request.form.get("companyName", "").strip()
        contact_name = request.form.get("contactName", "").strip()
        email = request.form.get("email", "").strip()
        phone = request.form.get("phone", "").strip()
        website = request.form.get("website", "").strip()
        verification_info = request.form.get("verificationInfo", "").strip()
        agreement = request.form.get("agreement") == "on"

        # Validate form data
        errors = []

        # Company Name validation
        if len(company_name) < 2 or len(company_name) > 100:
            errors.append("Company name must be between 2-100 characters")

        # Contact Person validation
        if len(contact_name) < 2 or len(contact_name) > 50:
            errors.append("Contact name must be between 2-50 characters")

        # Email validation
        if not is_valid_email(email):
            errors.append("Please enter a valid work email address")

        # Phone validation (using your existing pattern)
        if not PHONE_REGEX.match(phone):
            errors.append("Please enter a valid international phone number")

        # Website validation
        try:
            result = urlparse(website)
            if not all([result.scheme, result.netloc]):
                errors.append("Please enter a valid website URL")
        except:
            errors.append("Please enter a valid website URL")

        # Agreement check
        if not agreement:
            errors.append("You must agree to post only genuine job opportunities")

        if errors:
            for error in errors:
                flash(error, "danger")
            return redirect(url_for("employer_signup"))

        try:
            # Database operations
            conn, cur = ats_safe_connect()

            # Generate UUID for potential employer id
            potential_employer_id = str(uuid.uuid4())

            cur.execute(
                """INSERT INTO feed_potentialemployer
                (id, company_name, contact_name, email, phone, website,
                 verification_info, created_at, status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), %s)""",
                (
                    potential_employer_id,
                    company_name,
                    contact_name,
                    email,
                    phone,
                    website,
                    verification_info,
                    "Pending",  # Status
                ),
            )
            conn.commit()
            cur.close()

            flash(
                "Registration submitted successfully! We'll contact you for verification within 2 business days.",
                "success",
            )

            # Send confirmation email (pseudo-code)
            send_emp_verification_email(email, company_name)

            return redirect(url_for("employer_signup"))

        except Exception as e:
            app.logger.error(f"Employer registration error: {str(e)}")
            flash("An error occurred during registration. Please try again.", "danger")
            return redirect(url_for("employer_signup"))

    # GET request - show form
    return render_template("register_company.html")


def send_emp_verification_email(email, company_name):
    # Pseudo-code to send email
    print(f"Sending verification email to {email} for {company_name}")
    # Implement your email sending logic here
    # For example, using Flask-Mail or any other email service
    pass


@app.route("/talent-pool", methods=["GET", "POST"])
def talent_pool():
    if request.method == "POST":
        # Get form data
        first_name = request.form.get("firstName", "").strip()
        last_name = request.form.get("lastName", "").strip()
        phone = request.form.get("phone", "").strip()
        email = request.form.get("email", "").strip()
        location = request.form.get("location", "").strip()
        cv_file = request.files.get("cvUpload")

        # GDPR Consent data
        consent_future_opportunities = request.form.get("consent_future_opportunities") == "true"

        # Validate form data
        errors = []

        # Validate first and last name
        if len(first_name) < 2 or len(first_name) > 50:
            errors.append("First name must be between 2-50 characters")
        if len(last_name) < 2 or len(last_name) > 50:
            errors.append("Last name must be between 2-50 characters")

        # Validate phone number (optional)
        if phone and not PHONE_REGEX.match(phone):
            errors.append("Please enter a valid international phone number")

        # Validate email
        if not is_valid_email(email):
            errors.append("Please enter a valid email address")

        # Validate location
        if not location:
            errors.append("Please select your country of residence")

        # Validate CV file
        if not cv_file or cv_file.filename == "":
            errors.append("Please select a CV file to upload")

        if errors:
            for error in errors:
                flash(error, "danger")
            return redirect(url_for("talent_pool"))

        try:
            # Save the CV in a safe and accessible location
            if cv_file.filename != "":
                cv_name = secure_filename(f"{email}_.pdf")
                cv_name = f"{os.urandom(8).hex()}_{cv_name}"
                local_cv_location = "./cv_storage/{}".format(cv_name)
                cloud_cv_location = "{}/{}".format("candidate-pool", cv_name)
                cv_file.save(local_cv_location)

                # Upload to S3 with error handling
                if client:
                    try:
                        client.upload_file(
                            local_cv_location, "applicants-cv-bucket", cloud_cv_location
                        )
                        print(f"CV uploaded successfully to: {cloud_cv_location}", flush=True)
                    except Exception as upload_error:
                        print(f"Error uploading CV to S3: {str(upload_error)}", flush=True)
                        print(f"S3 upload failed, but continuing with application process", flush=True)
                        # Continue with application process even if S3 upload fails
                else:
                    print("S3 client not available, CV stored locally only", flush=True)

                if os.path.exists(local_cv_location):
                    os.remove(local_cv_location)
                else:
                    print("The file does not exist")

            # Insert data into the feed_talentpool table
            conn, cur = ats_safe_connect()

            # Generate UUID for talent_id
            talent_id = str(uuid.uuid4())

            cur.execute(
                """INSERT INTO feed_talentpool
                (talent_id, talent_firstname, talent_lastname, talent_phone, talent_email,
                 talent_country, cv_location, talent_status, talent_added_at,
                 consent_future_opportunities, consent_recorded_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, NOW());""",
                (
                    talent_id,
                    first_name,
                    last_name,
                    phone,
                    email,
                    location,
                    cloud_cv_location,
                    "Active",
                    consent_future_opportunities,
                ),
            )
            conn.commit()
            cur.close()

            flash(
                "Successfully registered in the talent pool! Employers may contact you soon.",
                "success",
            )
            return redirect(url_for("talent_pool"))

        except Exception as e:
            app.logger.error(f"Error processing registration: {str(e)}")
            flash("An error occurred during registration. Please try again.", "danger")
            return redirect(url_for("talent_pool"))

    # GET request - show form
    return render_template("talent_pool.html")


@app.route("/about")
def about():
    return render_template("about.html")


@app.route("/jobs")
def all_jobs():

    # Use cached RSS data instead of live fetching for better performance
    we_work_remotely_vacancies = rss_manager.get_cached_vacancies()

    conn, cur = ats_safe_connect()

    query = "SELECT * FROM vacancies WHERE vacancy_status = 'Active' LIMIT 200;"

    try:
        cur.execute(query)
    except:
        conn, cur = ats_safe_connect()
        cur.execute(query)

    columns = [col[0] for col in cur.description]
    vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    # Combine the converted vacancies with our existing vacancies
    vacancy_list = vacancies + we_work_remotely_vacancies

    all_tags = {}
    for vac in vacancy_list:
        try:
            vac["listing_source"]
            vac["listing_source"] = vac["listing_source"]
        except:
            vac["listing_source"] = "Canvider"

        if vac["jobtags"] is not None:
            try:
                all_tags[vac["vacancy_id"]] = json.loads(vac["jobtags"])
            except:
                all_tags[vac["vacancy_id"]] = []
        else:
            all_tags[vac["vacancy_id"]] = []

    query = "SELECT * FROM all_job_locations"

    try:
        cur.execute(query)
    except:
        conn, cur = ats_safe_connect()
        cur.execute(query)

    columns = [col[0] for col in cur.description]
    locations = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    return render_template(
        "jobs.html",
        all_vacancies=vacancy_list,
        all_tags=all_tags,
        all_locations=locations,
    )



@app.route("/filterjobs")
def filter_jobs():
    query = request.args.get("keyword-input", "").lower()
    query = f"%{query}%"

    # Get location input and normalize it
    location_input = (request.args.get("loc-select", "") or request.args.get("location-input", "")).strip()
    
    # Check if remote toggle is enabled
    include_remote = request.args.get("remote-toggle") == "on"

    # Parse and normalize location input
    location_parts = {}
    if location_input:
        # Split on comma and clean each part
        parts = [part.strip() for part in location_input.split(",")]
        
        # Try to identify city and country
        if len(parts) >= 2:
            location_parts["city"] = f"%{parts[0]}%"
            location_parts["country"] = f"%{parts[-1]}%"
        else:
            # Single location term - search in both fields
            location_parts["city"] = location_parts["country"] = f"%{parts[0]}%"

    conn, cur = ats_safe_connect()

    # Build base query
    base_query = """
        SELECT DISTINCT *
        FROM vacancies
        WHERE (vacancy_title ILIKE %s OR employer_name ILIKE %s)
          AND vacancy_status = 'Active'
    """
    query_params = [query, query]

    # Add location filters if provided
    if location_parts:
        base_query += """
          AND (
            vacancy_country ILIKE %s
            AND vacancy_city ILIKE %s
            OR (
              vacancy_job_description ILIKE %s 
              AND vacancy_job_description ILIKE %s
            )
          )
        """
        query_params.extend([location_parts["country"], location_parts["city"], location_parts["city"], location_parts["country"]])

    # Add sorting and limit
    base_query += """
        ORDER BY vacancy_id DESC
        LIMIT 500;
    """

    # Execute query
    cur.execute(base_query, query_params)
    columns = [col[0] for col in cur.description]
    vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    # Handle remote jobs based on toggle - use cached data
    we_work_remotely_vacancies = []
    if include_remote:
        we_work_remotely_vacancies = rss_manager.get_cached_vacancies(keyword=query)

        # Filter RSS vacancies by location if location filter is provided
        if location_input and location_input != "":
            filtered_rss_vacancies = []
            for rss_vac in we_work_remotely_vacancies:
                # Check if location matches in RSS vacancy
                rss_location = f"{rss_vac.get('vacancy_city', '')} {rss_vac.get('vacancy_country', '')}".lower()
                if location_input.lower() in rss_location or any(part.strip().lower() in rss_location for part in location_input.split(",")):
                    filtered_rss_vacancies.append(rss_vac)
            we_work_remotely_vacancies = filtered_rss_vacancies

    # Also filter database jobs for remote positions when toggle is enabled
    if include_remote:
        # Add remote jobs from database
        conn, cur = ats_safe_connect()
        remote_keywords = ['remote', 'worldwide', 'anywhere', 'global', 'work from home', 'wfh']

        if location_input and location_input != "":
            # Prepare query_country and query_city from location_parts
            query_country = location_parts.get("country", "%")
            query_city = location_parts.get("city", "%")
            # Include jobs that match location OR are remote
            cur.execute(
                """
                SELECT DISTINCT *
                FROM vacancies
                WHERE (vacancy_title ILIKE %s OR employer_name ILIKE %s)
                  AND vacancy_status = 'Active'
                  AND (
                    (vacancy_country ILIKE %s AND vacancy_city ILIKE %s)
                    OR (
                      vacancy_country ILIKE ANY(%s)
                      OR vacancy_city ILIKE ANY(%s)
                      OR office_schedule ILIKE ANY(%s)
                      OR vacancy_title ILIKE ANY(%s)
                    )
                  )
                ORDER BY vacancy_id ASC
                LIMIT 200;
                """,
                (query, query, query_country, query_city,
                 [f"%{kw}%" for kw in remote_keywords],
                 [f"%{kw}%" for kw in remote_keywords],
                 [f"%{kw}%" for kw in remote_keywords],
                 [f"%{kw}%" for kw in remote_keywords]),
            )
        else:
            # Include all jobs plus specifically remote ones
            cur.execute(
                """
                SELECT *
                FROM vacancies
                WHERE (vacancy_title ILIKE %s OR employer_name ILIKE %s)
                  AND vacancy_status = 'Active'
                ORDER BY vacancy_id ASC
                LIMIT 200;
                """,
                (query, query),
            )

        columns = [col[0] for col in cur.description]
        remote_vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

        # Merge and deduplicate
        vacancy_ids = {v['vacancy_id'] for v in vacancies}
        for rv in remote_vacancies:
            if rv['vacancy_id'] not in vacancy_ids:
                vacancies.append(rv)

        cur.close()

    all_tags = {}
    for vac in vacancies:
        try:
            vac["listing_source"]
            vac["listing_source"] = vac["listing_source"]
        except:
            vac["listing_source"] = "Canvider"
        if vac["jobtags"] is not None:
            try:
                all_tags[vac["vacancy_id"]] = json.loads(vac["jobtags"])
            except:
                all_tags[vac["vacancy_id"]] = []
        else:
            all_tags[vac["vacancy_id"]] = []

    vacancy_list = vacancies + we_work_remotely_vacancies
    return render_template(
        "jobs_list.html", all_vacancies=vacancy_list, all_tags=all_tags
    )


# Route of the vacancy description and application form
@app.route("/apply/<vacancy_id>")
def view_vacancy(vacancy_id):
    # Get source parameter from URL if present
    source = request.args.get("source", "direct")
    if source == "direct":
        source = "workloupe"

    conn, cur = ats_safe_connect()

    cur.execute(
        "SELECT * FROM feed_vacancy WHERE vacancy_id = %s LIMIT 1",
        (vacancy_id,)
    )
    columns = [col[0] for col in cur.description]
    vacancy = [dict(zip(columns, row)) for row in cur.fetchall()][0]

    cur.close()

    # get employer info
    conn, cur = ats_safe_connect()
    cur.execute(
        "SELECT * FROM feed_employer WHERE employer_id = %s LIMIT 1",
        (vacancy["employer_id"],)
    )
    columns = [col[0] for col in cur.description]
    employer = [dict(zip(columns, row)) for row in cur.fetchall()][0]
    cur.close()

    all_tags = {}
    if vacancy["jobtags"] is not None:
        try:
            tags = json.loads(vacancy["jobtags"])
            all_tags[vacancy["vacancy_id"]] = tags
        except:
            all_tags[vacancy["vacancy_id"]] = []
    else:
        all_tags[vacancy["vacancy_id"]] = []

    if vacancy["vacancy_status"] == "Active":
        return render_template(
            "vacancy.html", vacancy=vacancy, employer=employer, all_tags=all_tags, source=source
        )
    else:
        return render_template(
            "vacancy_closed.html", vacancy=vacancy, all_tags=all_tags, source=source
        )


# Route to handle form submission
@app.route("/submit", methods=["POST"])
def submit():
    print("Submitting application", flush=True)
    # Collect data from the form
    fname = request.form.get("fname", "").strip()
    lname = request.form.get("lname", "").strip()
    email = request.form.get("email", "").strip()
    phone = request.form.get("phone", "").strip()
    notice = request.form.get("notice", "").strip()
    applicant_cv = request.files.get("cv")
    applied_vacancy_id = request.form.get("vac_id", "").strip()
    source = request.form.get("source", "").strip()

    # GDPR Consent data
    consent_future_opportunities = request.form.get("consent_future_opportunities") == "true"

    # Validate form data
    errors = []

    # Validate first name
    if not fname or len(fname) < 2 or len(fname) > 50:
        errors.append("First name must be between 2-50 characters")

    # Validate last name
    if not lname or len(lname) < 2 or len(lname) > 50:
        errors.append("Last name must be between 2-50 characters")

    # Validate email with enhanced validation
    if not email:
        errors.append("Email address is required")
    elif not is_valid_email(email):
        errors.append("Please enter a valid email address")

    # Validate phone number (optional but if provided, must be valid)
    if phone and not PHONE_REGEX.match(phone):
        errors.append("Please enter a valid international phone number")

    # Validate CV file
    if not applicant_cv or applicant_cv.filename == "":
        errors.append("Please upload your CV/resume")
    elif not allowed_file(applicant_cv.filename):
        errors.append("Please upload a PDF file for your CV/resume")

    # Validate vacancy ID
    if not applied_vacancy_id:
        errors.append("Invalid job application")

    # If there are validation errors, redirect back with error messages
    if errors:
        for error in errors:
            flash(error, "danger")
        return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))

    print(f"Application validation passed for email: {email}", flush=True)

    # Save the CV in safe and accessible location
    cv_text = ""
    cloud_cv_location = ""
    if applicant_cv.filename != "":
        # Create consistent filename format
        cv_name = secure_filename("{}_.pdf".format(email))
        local_cv_location = "./cv_storage/{}".format(cv_name)

        # Use consistent cloud path format that matches ATS expectations
        # ATS expects: vacancy_id/filename format
        cloud_cv_location = "{}/{}".format(applied_vacancy_id, cv_name)

        applicant_cv.save(local_cv_location)

        # Extract text from PDF
        try:
            cv_text = extract_text_from_pdf(local_cv_location)
        except Exception as e:
            print(f"Error extracting text from PDF: {str(e)}", flush=True)
            cv_text = "Error extracting text"

        # Upload to S3 with improved error handling
        if client:
            try:
                client.upload_file(
                    local_cv_location, "applicants-cv-bucket", cloud_cv_location
                )
                print(f"CV uploaded successfully to: {cloud_cv_location}", flush=True)

                # Delete the local file after successful upload
                if os.path.exists(local_cv_location):
                    os.remove(local_cv_location)
                else:
                    print("The resume file did not exist locally...", flush=True)

            except Exception as e:
                print(f"Error uploading file to S3: {str(e)}", flush=True)
                print(f"Attempted to upload to: {cloud_cv_location}", flush=True)
                print("S3 upload failed, but continuing with application process", flush=True)
                # Continue with application process even if S3 upload fails
                # Delete local file anyway to prevent disk space issues
                if os.path.exists(local_cv_location):
                    os.remove(local_cv_location)
        else:
            print("S3 client not available, CV stored locally only", flush=True)
            # Still delete local file to prevent disk space issues
            if os.path.exists(local_cv_location):
                os.remove(local_cv_location)


    try:
        print("CV is getting censored")
        censor = UnicodeAwareCVCensor()
        cv_text = censor.censor_cv(cv_text, fname, lname)
        print("CV is censored")
    except Exception as e:
        print(f"Error censoring CV: {str(e)}", flush=True)

    # Insert candidate
    try:
        conn, cur = ats_safe_connect()
        def_adress = "Place, Holder"
        def_date_of_birth = "1111-01-01"

        # Generate a random number between 10 and 60 for dark avatar background
        rand_a, rand_b, rand_c = (
            random.randint(10, 60),
            random.randint(10, 60),
            random.randint(10, 60),
        )
        def_avatar_bg_color = "#{}{}{}".format(str(rand_a), str(rand_b), str(rand_c))

        # Generate UUID for candidate_id
        candidate_id = str(uuid.uuid4())

        cur.execute(
            """INSERT INTO feed_candidate
                (candidate_id, candidate_firstname, candidate_lastname, candidate_email,
                candidate_phone, candidate_address, candidate_date_of_birth,
                avatar_bg_color, candidate_created_at)
                VALUES (%s, %s, %s, %s, %s, %s, TO_TIMESTAMP(%s, 'YYYY-MM-DD'), %s, NOW())
                RETURNING candidate_id;""",
            (
                candidate_id,
                fname,
                lname,
                email,
                phone if phone else None,
                def_adress,
                def_date_of_birth,
                def_avatar_bg_color
            )
        )
        result = cur.fetchone()
        if not result:
            raise Exception("Failed to get candidate_id after insertion")
        candidate_id = result[0]
        conn.commit()
        cur.close()
    except Exception as e:
        print(f"Error inserting candidate: {str(e)}", flush=True)
        flash(
            "An error occurred during application submission. Please try again.",
            "danger",
        )
        return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))

    # Insert application
    try:
        conn, cur = ats_safe_connect()
        application_source = str(source).lower()
        application_status = "Active"
        application_state = "New"
        current_employer = "Unknown"
        current_position = "Unknown"
        education_level = "Unknown"
        total_exp_years = 0

        # Generate UUID for application_id
        application_id = str(uuid.uuid4())

        cur.execute(
            """ INSERT INTO feed_application (
                                    application_id,
                                    candidate_id,
                                    vacancy_id,
                                    application_date,
                                    application_source,
                                    application_status,
                                    application_state,
                                    current_employer,
                                    current_position,
                                    education_level,
                                    notice_period,
                                    total_exp_years,
                                    score,
                                    cv_location,
                                    consent_future_opportunities,
                                    consent_recorded_at
                                    ) VALUES (%s,%s,%s, NOW(),%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,NOW()) RETURNING application_id; """,
            (
                application_id,
                candidate_id,
                applied_vacancy_id,
                application_source,
                application_status,
                application_state,
                current_employer,
                current_position,
                education_level,
                notice,
                total_exp_years,
                -1,
                cloud_cv_location,
                consent_future_opportunities,
            ),
        )
        result = cur.fetchone()
        if not result:
            raise Exception("Failed to get application_id after insertion")
        application_id = result[0]
        conn.commit()
        cur.close()
    except Exception as e:
        print(f"Error inserting application: {str(e)}", flush=True)
        flash(
            "An error occurred during application submission. Please try again.",
            "danger",
        )
        return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))

    # Insert application state
    try:
        conn, cur = ats_safe_connect()

        # Generate UUID for state_id
        state_id = str(uuid.uuid4())

        cur.execute(
            """ INSERT INTO feed_applicationstate (
                                    state_id,
                                    state_name,
                                    state_notes,
                                    state_started_at,
                                    application_id,
                                    committed_by_id)
                                    VALUES (%s,%s,%s,NOW(),%s,%s); """,
            (
                state_id,
                application_state,
                "Application submitted from the workloupe.com",
                application_id,
                "15",
            ),
        )
        conn.commit()
        cur.close()
    except Exception as e:
        print(f"Error inserting application state: {str(e)}", flush=True)

    # Insert CV text
    try:
        conn, cur = ats_safe_connect()

        # Generate UUID for cv_text_id
        cv_text_id = str(uuid.uuid4())

        cur.execute(
            """
        INSERT INTO feed_applicationcvtext(
                    cv_text_id,
                    application_id,
                    cv_text,
                    cv_text_date,
                    is_cv_analyzed,
                    cv_location)
                    VALUES (%s,%s,%s,NOW(),%s,%s);
        """,
            (
                cv_text_id,
                application_id,
                cv_text,
                False,
                cloud_cv_location,
            ),
        )
        conn.commit()
        cur.close()
    except Exception as e:
        print(f"Error inserting CV text: {str(e)}")

    # Send confirmation email
    try:
        # Get employer ID based on feed_vacancy (it has employer_id)
        conn, cur = ats_safe_connect()
        cur.execute(
            "SELECT employer_id, vacancy_title FROM feed_vacancy WHERE vacancy_id = %s;",
            (applied_vacancy_id,),
        )
        employer_id, vacancy_title = cur.fetchone()
        cur.close()

        # Get employer name based on employer_id
        conn, cur = ats_safe_connect()
        cur.execute(
            "SELECT employer_name FROM feed_employer WHERE employer_id = %s;",
            (employer_id,),
        )
        employer_name = cur.fetchone()[0]
        cur.close()

        send_app_confirmation_email(
            recipient=email,
            fname=fname,
            vacancy_id=applied_vacancy_id,
            application_id=application_id,
            employer_id=employer_id,
            employer_name=employer_name,
            vacancy_title=vacancy_title,
        )
    except Exception as e:
        print(f"Error sending email: {str(e)}")

    # Check if the candidate is already in the talent pool
    try:
        conn, cur = ats_safe_connect()
        cur.execute("SELECT * FROM feed_talentpool WHERE talent_email = %s;", (email,))
        in_talent_pool = cur.fetchone() is not None
        cur.close()

        if in_talent_pool:
            flash("Successfully applied for the job!", "success")
            return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))
        else:
            flash(
                "Successfully applied for the job! Consider joining our talent pool to attract interest from the employers.",
                "success",
            )
            return redirect(url_for("talent_pool"))
    except Exception as e:
        print(f"Error checking talent pool: {str(e)}")
        flash("Application submitted successfully!", "success")
        return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))


@app.route("/employers")
def employers():
    conn, cur = ats_safe_connect()

    cur.execute("SELECT * FROM employer_cards WHERE open_positions > 0 ORDER BY open_positions DESC LIMIT 50;")
    columns = [col[0] for col in cur.description]
    all_employers = [dict(zip(columns, row)) for row in cur.fetchall()]

    # Calculate total open positions
    total_open_positions = sum(employer.get('open_positions', 0) for employer in all_employers)

    # Ensure minimum value for display
    total_open_positions = max(total_open_positions, 100)  # Show at least 100+ for marketing

    cur.close()
    return render_template("employers.html", all_employers=all_employers, total_open_positions=total_open_positions)


@app.route("/employers/<employer_id>")
def employer(employer_id):

    conn, cur = ats_safe_connect()

    cur.execute(
        "SELECT * FROM feed_employer WHERE employer_id = %s LIMIT 1;",
        (employer_id,)
    )
    columns = [col[0] for col in cur.description]
    employer = [dict(zip(columns, row)) for row in cur.fetchall()][0]

    social_portals = []
    try:
        if employer["employer_social_portals"]:
            for i in str(employer["employer_social_portals"]).split(","):
                try:
                    i = i.strip()
                    splitted = i.split(";")
                    if len(splitted) >= 2:
                        portal, url = splitted[0], splitted[1]
                        social_portals.append({"url": url, "location": portal})
                except Exception as e:
                    app.logger.warning(f"Error processing social portal entry: {str(e)}")
                    continue
    except Exception as e:
        app.logger.error(f"Error processing employer social portals: {str(e)}")

    locations = []
    ## Make popover in future.
    office_count = 0
    for i in str(employer["office_locations"]).split("|"):
        if office_count > 4:
            locations.append({"location": "+ More Locations"})
            break
        else:
            i = i.strip()
            office_count = office_count + 1
            locations.append({"location": i})

    all_photos = client.list_objects_v2(
        Bucket="canvider-public", Prefix=f"company-galleries/{employer_id}/"
    )
    try:
        gallery = [i["Key"] for i in all_photos["Contents"][1:]]
    except:
        gallery = []

    cur.execute(
        "SELECT * FROM feed_vacancy WHERE employer_id = %s AND vacancy_status = 'Active';",
        (employer_id,)
    )
    columns = [col[0] for col in cur.description]
    vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    try:
        return render_template(
            "company.html",
            employer=employer,
            vacancies=vacancies,
            social_portals=social_portals,
            locations=locations,
            all_gallery=gallery,
        )
    except:
        return render_template_string("404")


def ats_safe_connect():
    print("Connecting to the database...")
    conn = psycopg2.connect(dsn=DATABASE_URL)

    try:
        conn.autocommit = False  # Explicit transactions
        cur = conn.cursor()
    except:
        print("Reconnecting to the database...")
        ats_safe_connect()

    return conn, cur

def extract_text_from_pdf(file_path):
    text = ""
    try:
        # First try with PyPDF
        reader = PdfReader(file_path)
        for page in reader.pages:
            page_text = page.extract_text()
            text += page_text if page_text else ""

        # If PyPDF returned empty or very little text, try OCR
        if len(text.strip()) < 50:  # Adjust threshold as needed
            print("PyPDF extraction yielded little text, trying OCR...", flush=True)
            images = pdf2image.convert_from_path(file_path)
            ocr_text = ""
            for img in images:
                ocr_text += pytesseract.image_to_string(img)

            # Use OCR result if it's better than PyPDF
            if len(ocr_text.strip()) > len(text.strip()):
                text = ocr_text
    except Exception as e:
        print(f"Error extracting text from PDF: {str(e)}", flush=True)

    return text


@app.route("/search-locations")
def search_locations():
    """API endpoint for location autocomplete"""
    query = request.args.get("q", "").strip()
    if len(query) < 2:
        return {"locations": []}

    conn, cur = ats_safe_connect()

    # Search in both database locations and add common locations
    cur.execute(
        """
        SELECT DISTINCT
            CASE
                WHEN vacancy_city IS NOT NULL AND vacancy_city != ''
                THEN vacancy_city || ', ' || vacancy_country
                ELSE vacancy_country
            END as location
        FROM vacancies
        WHERE vacancy_status = 'Active'
          AND (vacancy_city ILIKE %s OR vacancy_country ILIKE %s)
        ORDER BY location
        LIMIT 10;
        """,
        (f"%{query}%", f"%{query}%"),
    )

    locations = [row[0] for row in cur.fetchall()]
    cur.close()

    # Add some common remote/global options
    common_locations = [
        "Remote",
        "Worldwide",
        "Global",
        "United States",
        "United Kingdom",
        "Canada",
        "Germany",
        "Netherlands",
        "Australia"
    ]

    # Filter common locations by query
    matching_common = [loc for loc in common_locations if query.lower() in loc.lower()]

    # Combine and deduplicate
    all_locations = list(dict.fromkeys(matching_common + locations))[:10]

    return {"locations": all_locations}


@app.route("/job-alerts", methods=["GET", "POST"])
def job_alerts():
    """Handle job alert creation and management."""
    if request.method == "POST":
        # Get form data
        email = request.form.get("email", "").strip()
        keywords = request.form.get("keywords", "").strip()
        location = request.form.get("location", "").strip()
        remote_only = request.form.get("remote_only") == "on"
        salary_min = request.form.get("salary_min", "").strip()

        # Validate form data
        errors = []

        # Email validation
        if not is_valid_email(email):
            errors.append("Please enter a valid email address")

        # Convert salary to integer if provided
        salary_min_int = None
        if salary_min:
            try:
                salary_min_int = int(salary_min)
                if salary_min_int < 0:
                    errors.append("Salary must be a positive number")
            except ValueError:
                errors.append("Please enter a valid salary amount")

        if errors:
            for error in errors:
                flash(error, "danger")
            return redirect(url_for("job_alerts"))

        # Create job alert
        alert = notification_system.create_job_alert(
            email=email,
            keywords=keywords if keywords else None,
            location=location if location else None,
            remote_only=remote_only,
            salary_min=salary_min_int
        )

        if alert:
            flash("Job alert created successfully! You'll receive email notifications for matching jobs.", "success")
        else:
            flash("Failed to create job alert. Please try again.", "danger")

        return redirect(url_for("job_alerts"))

    # GET request - show form and user's existing alerts
    user_email = request.args.get("email", "")
    user_alerts = []
    if user_email and is_valid_email(user_email):
        # Get alerts and send them via email
        alerts = notification_system.get_user_alerts(user_email)
        if alerts:
            # Also process job alerts for this user to send any matching jobs
            try:
                alert_stats = notification_system.process_alerts_for_user(user_email)
                if alert_stats.get('emails_sent', 0) > 0:
                    flash(f"Found {alert_stats['emails_sent']} new job matches! Check your email for details.", "success")
            except Exception as e:
                app.logger.error(f"Error processing alerts for user {user_email}: {str(e)}")

            # Prepare email content with deactivation links
            email_body = "Your current job alerts:\n\n"
            for alert in alerts:
                email_body += f"- Keywords: {alert.keywords or 'Any'}\n"
                email_body += f"  Location: {alert.location or 'Any'}\n"
                email_body += f"  Remote Only: {'Yes' if alert.remote_only else 'No'}\n"
                email_body += f"  Min Salary: ${alert.salary_min:,}\n" if alert.salary_min else "  Min Salary: Not specified\n"
                # Add deactivation link
                deactivate_url = url_for('deactivate_alert', alert_id=alert.id, email=user_email, _external=True)
                email_body += f"  Deactivate this alert: {deactivate_url}\n"
                email_body += "\n"

            # Send email using redmail
            try:
                sender = os.getenv("MAIL_USERNAME")
                r_email = redmail.EmailSender(
                    host=os.getenv("SMTP_MAIL_HOST"),
                    port=os.getenv("SMTP_MAIL_PORT"),
                    username=sender,
                    password=os.getenv("MAIL_PASSWORD"),
                )
                r_email.connect()
                r_email.send(
                    subject="Your Job Alerts on Workloupe",
                    sender=f"Workloupe Job Alerts <{sender}>",
                    text=email_body,
                    receivers=user_email,
                )
                flash("Your saved job alerts have been sent to your email address.", "success")
            except Exception as e:
                flash("Failed to send job alerts email. Please try again.", "danger")
        else:
            flash("No active job alerts found for your email address.", "info")

    return render_template("job_alerts.html", user_alerts=user_alerts, user_email=user_email)


@app.route("/deactivate-alert/<int:alert_id>")
def deactivate_alert(alert_id):
    """Deactivate a job alert."""
    email = request.args.get("email", "")

    if not email or not is_valid_email(email):
        flash("Invalid email address", "danger")
        return redirect(url_for("job_alerts"))

    success = notification_system.deactivate_alert(alert_id, email)

    if success:
        flash("Job alert deactivated successfully", "success")
    else:
        flash("Failed to deactivate job alert", "danger")

    return redirect(url_for("job_alerts", email=email))


@app.route("/admin/rss-update")
def manual_rss_update():
    """Manual RSS feed update for admin purposes."""
    # Simple admin check - in production, implement proper authentication
    admin_key = request.args.get("key", "")
    if admin_key != os.getenv("ADMIN_KEY", "admin123"):
        return "Unauthorized", 401

    try:
        stats = rss_manager.update_all_feeds()
        return jsonify({
            "status": "success",
            "message": f"RSS feeds updated successfully. Cached {stats.get('total_cached', 0)} jobs.",
            "stats": stats
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"RSS update failed: {str(e)}"
        }), 500


@app.route("/admin/process-alerts")
def manual_alert_processing():
    """Manual job alert processing for admin purposes."""
    # Simple admin check - in production, implement proper authentication
    admin_key = request.args.get("key", "")
    if admin_key != os.getenv("ADMIN_KEY", "admin123"):
        return "Unauthorized", 401

    try:
        stats = notification_system.process_all_alerts()
        return jsonify({
            "status": "success",
            "message": f"Job alerts processed successfully. Sent {stats.get('emails_sent', 0)} emails.",
            "stats": stats
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Alert processing failed: {str(e)}"
        }), 500


@app.route("/filteremp")
def filter_employers():
    query = request.args.get("keyword-input", "").lower()
    query = f"%{query}%"

    conn, cur = ats_safe_connect()

    cur.execute(
        """
    SELECT *
    FROM employer_cards
    WHERE employer_name ILIKE %s OR employer_industry ILIKE %s OR headquarter ILIKE %s
    AND open_positions > 0
    ORDER BY open_positions DESC
    LIMIT 50;
    """,
        (
            query,
            query,
            query,
        ),
    )

    columns = [col[0] for col in cur.description]
    filtered_employers = [dict(zip(columns, row)) for row in cur.fetchall()]

    return render_template("employers_list.html", all_employers=filtered_employers)


class ExtendedRSSFeed(feedgenerator.Rss201rev2Feed):
    def add_item(self, **kwargs):
        # Extract custom fields
        employer_name = kwargs.pop("employer_name", None)
        country = kwargs.pop("country", None)
        city = kwargs.pop("city", None)
        salary_min = kwargs.pop("salary_min", None)
        salary_max = kwargs.pop("salary_max", None)
        salary_currency = kwargs.pop("salary_currency", None)
        office_schedule = kwargs.pop("office_schedule", None)
        work_mode = kwargs.pop("work_mode", None)
        skills = kwargs.pop("skills", None)

        # Call parent add_item with standard fields
        item = super(ExtendedRSSFeed, self).add_item(**kwargs)

        # Add custom elements to the last item (the one we just added)
        if employer_name:
            self.items[-1]["employer_name"] = employer_name
        if country:
            self.items[-1]["country"] = country
        if city:
            self.items[-1]["city"] = city
        if salary_min:
            self.items[-1]["salary_min"] = salary_min
        if salary_max:
            self.items[-1]["salary_max"] = salary_max
        if salary_currency:
            self.items[-1]["salary_currency"] = salary_currency
        if office_schedule:
            self.items[-1]["office_schedule"] = office_schedule
        if work_mode:
            self.items[-1]["work_mode"] = work_mode
        if skills:
            self.items[-1]["skills"] = skills

        return item

    def write(self, outfile, encoding):
        # Add custom namespace
        handler = feedgenerator.SimplerXMLGenerator(outfile, encoding)
        handler.startDocument()
        handler.startElement("rss", self.rss_attributes())
        handler.startElement("channel", self.root_attributes())
        self.add_root_elements(handler)
        self.write_items(handler)
        self.endChannelElement(handler)
        handler.endElement("rss")

    def write_items(self, handler):
        for item in self.items:
            handler.startElement("item", {})

            # Add standard elements
            self.add_item_elements(handler, item)

            # Add custom elements with workloupe namespace
            if "employer_name" in item:
                handler.addQuickElement("employer", item["employer_name"])
            if "country" in item:
                handler.addQuickElement("country", item["country"])
            if "city" in item and item["city"]:
                handler.addQuickElement("city", item["city"])
            if "salary_min" in item:
                handler.addQuickElement("salary_min", str(item["salary_min"]))
            if "salary_max" in item:
                handler.addQuickElement("salary_max", str(item["salary_max"]))
            if "salary_currency" in item:
                handler.addQuickElement("salary_currency", item["salary_currency"])
            if "office_schedule" in item:
                handler.addQuickElement("office_schedule", item["office_schedule"])
            if "work_mode" in item:
                handler.addQuickElement("work_mode", item["work_mode"])
            if "skills" in item and item["skills"]:
                handler.addQuickElement("skills", item["skills"])

            handler.endElement("item")

    def rss_attributes(self):
        attrs = super(ExtendedRSSFeed, self).rss_attributes()
        attrs["xmlns:workloupe"] = "http://workloupe.com/ns/rss/1.0/"
        return attrs


@app.route("/<employer_name>/jobfeed.rss")
def jobfeed(employer_name):
    conn, cur = ats_safe_connect()

    cur.execute(
        """
        SELECT
            *
        FROM feed_employer
        WHERE employer_name = %s
        """,
        (employer_name,),
    )

    columns = [col[0] for col in cur.description]
    employer = [dict(zip(columns, row)) for row in cur.fetchall()][0]

    cur.close()

    conn, cur = ats_safe_connect()

    cur.execute(
        """
        SELECT
            *
        FROM feed_vacancy
        WHERE employer_id = %s AND vacancy_status = 'Active'
        ORDER BY vacancy_id DESC
        LIMIT 200;
        """,
        (str(employer["employer_id"]),),
    )

    columns = [col[0] for col in cur.description]
    vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    employer_name = employer["employer_name"]

    xml_output = '<?xml version="1.0" encoding="utf-8"?>\n<source>'

    for vacancy in vacancies:
        salary = (
            f"{vacancy['salary_min']}-{vacancy['salary_max']} {vacancy['salary_currency']}"
            if vacancy["salary_min"] and vacancy["salary_max"]
            else "Not specified"
        )
        location = (
            f"{vacancy['vacancy_city']},{vacancy['vacancy_country']}"
            if vacancy["vacancy_city"]
            else vacancy["vacancy_country"]
        )

        # Clean HTML tags and entities and remove a tags to not have empty links on the raw description and remove emojis.
        description_without_markups = vacancy["vacancy_job_description"]

        # Remove Emojis
        description_without_markups = description_without_markups.encode(
            "ascii", "ignore"
        ).decode("ascii")

        # remove a tags and anything inside of them to not have empty links on the raw description.
        description_without_markups = re.sub(
            "<a[^>]*>(.*?)</a>", "", description_without_markups
        )

        # Add spacing for headers with attribute support
        description_without_markups = re.sub(
            "<h1[^>]*>(.*?)</h1>", "\n\n\\1\n\n", description_without_markups
        )
        description_without_markups = re.sub(
            "<h2[^>]*>(.*?)</h2>", "\n\n\\1\n\n", description_without_markups
        )
        description_without_markups = re.sub(
            "<h3[^>]*>(.*?)</h3>", "\n\\1\n", description_without_markups
        )

        # Add spacing for paragraphs with attribute support
        description_without_markups = re.sub(
            "<p[^>]*>(.*?)</p>", "\\1\n\n", description_without_markups
        )

        # Add spacing for lists with attribute support
        description_without_markups = re.sub(
            "<li[^>]*>(.*?)</li>", "\n * \\1", description_without_markups
        )
        description_without_markups = re.sub(
            "<ul[^>]*>", "\n", description_without_markups
        )
        description_without_markups = re.sub(
            "</ul>", "\n\n", description_without_markups
        )
        description_without_markups = re.sub(
            "<ol[^>]*>", "\n", description_without_markups
        )
        description_without_markups = re.sub(
            "</ol>", "\n\n", description_without_markups
        )

        # Replace HTML space entities with actual spaces
        description_without_markups = description_without_markups.replace("&nbsp;", " ")

        # Remove remaining HTML tags and entities
        description_without_markups = re.sub("<[^>]+>", "", description_without_markups)
        description_without_markups = re.sub(
            "&lt;.*?&gt;", "", description_without_markups
        )
        description_without_markups = description_without_markups.strip()

        # Replace multiple spaces with single space and preserve line breaks
        description_clean_spaces = re.sub("[ \t]+", " ", description_without_markups)

        # Add proper line breaks for sections and preserve existing spacing
        clean_description = description_clean_spaces.replace(". ", ".\n\n").replace(
            "\n\n\n", "\n\n"
        )

        # Handle common section headers by ensuring they have space after them
        clean_description = re.sub(r"([A-Za-z]+):", r"\1: ", clean_description)

        # Ensure there's space between sentences that don't end with periods
        clean_description = re.sub(r"([a-z])([A-Z])", r"\1 \2", clean_description)

        # ensure that the first letter is capitalized
        clean_description = clean_description[0].upper() + clean_description[1:]

        # Ensure that the standard is UTF-8
        clean_description = clean_description.encode("utf-8", "ignore").decode("utf-8")

        xml_output += f"""
        <job>
            <title>{vacancy['vacancy_title']}</title>
            <date>{vacancy['vacancy_creation_date']}</date>
            <referencenumber>{vacancy['vacancy_id']}</referencenumber>
            <url>https://workloupe.com/apply/{vacancy['vacancy_id']}?source=postjobfree</url>
            <company>{employer_name}</company>
            <location>{location}</location>
            <description>{clean_description}</description>
            <salary>{salary}</salary>
            <email><EMAIL></email>
        </job>"""

    xml_output += "\n</source>"

    return xml_output, 200, {"Content-Type": "application/rss+xml"}


@app.route("/all_positions.rss")
def all_positions_rss():
    conn, cur = ats_safe_connect()

    # Get all active vacancies
    cur.execute(
        """
        SELECT v.vacancy_id, v.vacancy_title, v.vacancy_creation_date, v.vacancy_job_description, v.salary_min, v.salary_max, v.salary_currency, v.vacancy_city, v.vacancy_country, e.employer_name
        FROM feed_vacancy v
        JOIN feed_employer e ON v.employer_id::text = e.employer_id::text
        WHERE vacancy_status = 'Active'
        ORDER BY vacancy_id DESC
        LIMIT 1000;
        """
    )
    vacancies = [
        dict(zip([col[0] for col in cur.description], row)) for row in cur.fetchall()
    ]

    # Create XML response
    xml_output = """<?xml version="1.0" encoding="utf-8"?>
<source>
    <publisher>Workloupe</publisher>
    <publisherurl>https://workloupe.com</publisherurl>
    <timezone>UTC</timezone>
    <lastBuildDate>{}</lastBuildDate>""".format(
        datetime.datetime.now().strftime("%m/%d/%Y %I:%M:%S %p")
    )

    for vacancy in vacancies:
        # Get employer info
        xml_output += f"""
    <job>
        <title><![CDATA[{vacancy["vacancy_title"]}]]></title>
        <date><![CDATA[{vacancy["vacancy_creation_date"]}]]></date>
        <referencenumber><![CDATA[{vacancy["vacancy_id"]}]]></referencenumber>
        <url><![CDATA[https://workloupe.com/apply/{vacancy["vacancy_id"]}?source=feed]]></url>
        <company><![CDATA[{vacancy["employer_name"]}]]></company>
        <city><![CDATA[{vacancy["vacancy_city"]}]]></city>
        <country><![CDATA[{vacancy["vacancy_country"]}]]></country>
        <description><![CDATA[{vacancy["vacancy_job_description"]}]]></description>
        <salary><![CDATA[{vacancy["salary_min"]} - {vacancy["salary_max"]} {vacancy["salary_currency"]}]]></salary>
        <email><![CDATA[<EMAIL>]]></email>
    </job>"""

    xml_output += "\n</source>"

    cur.close()
    return xml_output, 200, {"Content-Type": "application/rss+xml"}


@app.route("/<employer_name>/jobs.rss")
def xml_feed(employer_name):
    ## workloupe based on workable style. It is not the same as the jobfeed.rss.
    conn, cur = ats_safe_connect()

    # Get employer info
    cur.execute(
        """SELECT * FROM feed_employer WHERE employer_name = %s""", (employer_name,)
    )
    employer = dict(zip([col[0] for col in cur.description], cur.fetchone()))

    # Get active vacancies for employer
    cur.execute(
        """SELECT * FROM feed_vacancy
           WHERE employer_id = %s AND vacancy_status = 'Active'
           ORDER BY vacancy_creation_date DESC""",
        (str(employer["employer_id"]),),
    )
    vacancies = [
        dict(zip([col[0] for col in cur.description], row)) for row in cur.fetchall()
    ]

    # Create XML response
    xml_output = f"""<?xml version="1.0" encoding="utf-8"?>
<source>
    <publisher>Workloupe</publisher>
    <publisherurl>https://workloupe.com</publisherurl>"""

    for vacancy in vacancies:
        xml_output += f"""
    <job>
        <title><![CDATA[{vacancy["vacancy_title"]}]]></title>
        <date><![CDATA[{vacancy["vacancy_creation_date"]}]]></date>
        <referencenumber><![CDATA[{vacancy["vacancy_id"]}]]></referencenumber>
        <url><![CDATA[https://workloupe.com/apply/{vacancy["vacancy_id"]}?source=feed]]></url>
        <company><![CDATA[{employer_name}]]></company>
        <city><![CDATA[{vacancy["vacancy_city"]}]]></city>
        <country><![CDATA[{vacancy["vacancy_country"]}]]></country>
        <remote><![CDATA[{"true" if "remote" in str(vacancy["office_schedule"]).lower() else "false"}]]></remote>
        <description><![CDATA[{vacancy["vacancy_job_description"]}]]></description>
        <website><![CDATA[https://workloupe.com/employers/{employer["employer_id"]}]]></website>
        <schedule><![CDATA[{str(vacancy["office_schedule"]).lower()}]]></schedule>
        <employment_type><![CDATA[{vacancy["work_schedule"] or "full-time"}]]></employment_type>
        <email><![CDATA[<EMAIL>]]></email>
        <salarymin><![CDATA[{vacancy["salary_min"]}]]></salarymin>
        <salarymax><![CDATA[{vacancy["salary_max"]}]]></salarymax>
        <salarycurrency><![CDATA[{vacancy["salary_currency"]}]]></salarycurrency>
        <salary><![CDATA[{vacancy["salary_min"]} - {vacancy["salary_max"]} {vacancy["salary_currency"]}]]></salary>
    </job>"""

    xml_output += """
</source>"""

    return xml_output, 200, {"Content-Type": "application/xml"}


def clean_description(description):
    # Clean the description to be more readable and easier to parse.
    if not description:
        return ""

        # Clean HTML tags and entities and remove a tags to not have empty links on the raw description and remove emojis.
    description_without_markups = description

    # Remove Emojis
    description_without_markups = description_without_markups.encode(
        "ascii", "ignore"
    ).decode("ascii")

    # remove a tags and anything inside of them to not have empty links on the raw description.
    description_without_markups = re.sub(
        "<a[^>]*>(.*?)</a>", "", description_without_markups
    )

    # Add spacing for headers with attribute support
    description_without_markups = re.sub(
        "<h1[^>]*>(.*?)</h1>", "\n\n\\1\n\n", description_without_markups
    )
    description_without_markups = re.sub(
        "<h2[^>]*>(.*?)</h2>", "\n\n\\1\n\n", description_without_markups
    )
    description_without_markups = re.sub(
        "<h3[^>]*>(.*?)</h3>", "\n\\1\n", description_without_markups
    )

    # Add spacing for paragraphs with attribute support
    description_without_markups = re.sub(
        "<p[^>]*>(.*?)</p>", "\\1\n\n", description_without_markups
    )

    # Add spacing for lists with attribute support
    description_without_markups = re.sub(
        "<li[^>]*>(.*?)</li>", "\n * \\1", description_without_markups
    )
    description_without_markups = re.sub("<ul[^>]*>", "\n", description_without_markups)
    description_without_markups = re.sub("</ul>", "\n\n", description_without_markups)
    description_without_markups = re.sub("<ol[^>]*>", "\n", description_without_markups)
    description_without_markups = re.sub("</ol>", "\n\n", description_without_markups)

    # Replace HTML space entities with actual spaces
    description_without_markups = description_without_markups.replace("&nbsp;", " ")

    # Remove remaining HTML tags and entities
    description_without_markups = re.sub("<[^>]+>", "", description_without_markups)
    description_without_markups = re.sub("&lt;.*?&gt;", "", description_without_markups)
    description_without_markups = description_without_markups.strip()

    # Replace multiple spaces with single space and preserve line breaks
    description_clean_spaces = re.sub("[ \t]+", " ", description_without_markups)

    # Add proper line breaks for sections and preserve existing spacing
    clean_description = description_clean_spaces.replace(". ", ".\n\n").replace(
        "\n\n\n", "\n\n"
    )

    # Handle common section headers by ensuring they have space after them
    clean_description = re.sub(r"([A-Za-z]+):", r"\1: ", clean_description)

    # Ensure there's space between sentences that don't end with periods
    clean_description = re.sub(r"([a-z])([A-Z])", r"\1 \2", clean_description)

    # ensure that the first letter is capitalized
    clean_description = clean_description[0].upper() + clean_description[1:]

    # Ensure that the standard is UTF-8
    clean_description = clean_description.encode("utf-8", "ignore").decode("utf-8")

    return clean_description

def clean_html_description(description):
    # Keep the HTML tags but clean all the styling and the unnecessary attributes
    # for example <h1 style="color: red;">Hello</h1> becomes <h1>Hello</h1>
    clean_html = description

    # remove a tags and anything inside of them to not have empty links on the raw description.
    clean_html = re.sub(
        "<a[^>]*>(.*?)</a>", "", clean_html
    )

    # Remove
    clean_html = re.sub("&lt;.*?&gt;", "", clean_html)

    # Replace HTML space entities with actual spaces
    clean_html = clean_html.replace("&nbsp;", " ")

    # Remove style attributes
    clean_html = re.sub(r'\s+style="[^"]*"', '', clean_html)

    # Remove font tags and their attributes
    clean_html = re.sub(r'<font[^>]*>', '', clean_html)
    clean_html = re.sub(r'</font>', '', clean_html)

    # Remove span tags and their attributes
    clean_html = re.sub(r'<span[^>]*>', '', clean_html)
    clean_html = re.sub(r'</span>', '', clean_html)

    # Remove div attributes but keep tags
    clean_html = re.sub(r'<div[^>]*>', '<div>', clean_html)

    # Standardize paragraph tags
    clean_html = re.sub(r'<p[^>]*>', '<p>', clean_html)
    clean_html = re.sub(r'</p>', '</p>\n\n', clean_html)

    # Standardize list tags
    clean_html = re.sub(r'<li[^>]*>', '<li>', clean_html)
    clean_html = re.sub(r'</li>', '</li>\n', clean_html)
    clean_html = re.sub(r'<ul[^>]*>', '<ul>', clean_html)
    clean_html = re.sub(r'</ul>', '</ul>\n\n', clean_html)
    clean_html = re.sub(r'<ol[^>]*>', '<ol>', clean_html)
    clean_html = re.sub(r'</ol>', '</ol>\n\n', clean_html)

    # Standardize header tags
    clean_html = re.sub(r'<h1[^>]*>', '<h1>', clean_html)
    clean_html = re.sub(r'</h1>', '</h1>\n\n', clean_html)
    clean_html = re.sub(r'<h2[^>]*>', '<h2>', clean_html)
    clean_html = re.sub(r'</h2>', '</h2>\n\n', clean_html)
    clean_html = re.sub(r'<h3[^>]*>', '<h3>', clean_html)
    clean_html = re.sub(r'</h3>', '</h3>\n\n', clean_html)
    clean_html = re.sub(r'<h4[^>]*>', '<h4>', clean_html)
    clean_html = re.sub(r'</h4>', '</h4>\n\n', clean_html)
    clean_html = re.sub(r'<h5[^>]*>', '<h5>', clean_html)
    clean_html = re.sub(r'</h5>', '</h5>\n\n', clean_html)
    clean_html = re.sub(r'<h6[^>]*>', '<h6>', clean_html)
    clean_html = re.sub(r'</h6>', '</h6>\n\n', clean_html)

    # Remove any remaining style-related attributes
    clean_html = re.sub(r'\s+(class|id|style|size|color|background-color|max-height|overflow-y)="[^"]*"', '', clean_html)

    # Clean up extra whitespace
    clean_html = re.sub(r'\s+', ' ', clean_html)

    return clean_html.strip()



@app.route("/all/jobs.rss")
def xml_feed_all():
    # Get active vacancies for employer that have PostJobFree in job_portals and join with location details
    conn, cur = ats_safe_connect()

    cur.execute(
        """SELECT * FROM (SELECT v.vacancy_id, v.vacancy_title, v.vacancy_creation_date, v.vacancy_status, v.vacancy_job_description,
        v.salary_min, v.salary_max, v.salary_currency, v.vacancy_city, v.vacancy_country, e.employer_id, e.employer_name
        FROM feed_vacancy v
        INNER JOIN feed_employer e ON v.employer_id::text = e.employer_id::text
        WHERE v.vacancy_status = 'Active' AND v.job_portals LIKE '%PostJobFree%'
        ) x
        INNER JOIN
        (
        SELECT * FROM feed_officelocation
        ) y
        ON
        x.vacancy_city::text = y.city::text
        AND x.employer_id::text = y.employer_id::text
        --y.country::text = x.vacancy_country::text
        """,
    )
    vacancies = [
        dict(zip([col[0] for col in cur.description], row)) for row in cur.fetchall()
    ]

    cur.close()

    # Process each vacancy to add clean description and location
    for vacancy in vacancies:
        # Clean description
        vacancy["clean_description"] = clean_description(
            vacancy["vacancy_job_description"]
        )
        # Parse location details
        if isinstance(vacancy["location_details"], dict):
            location_details = vacancy["location_details"]
        else:
            location_details = json.loads(vacancy["location_details"])

        # Extract location information
        vacancy["country_code"] = pycountry.countries.get(name=location_details["country"]).alpha_2
        vacancy["state"] = location_details["state"]
        vacancy["zip_code"] = location_details["postcode"]
        vacancy["location_full"] = location_details["formatted"]
        vacancy["postcode"] = location_details["postcode"]

    # Create XML response
    xml_output = f"""<?xml version="1.0" encoding="utf-8"?>
<source>
    <publisher>Workloupe</publisher>
    <publisherurl>https://workloupe.com</publisherurl>
    <timezone>UTC</timezone>
    <lastBuildDate>{datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")}</lastBuildDate>"""

    for vacancy in vacancies:
        xml_output += f"""
    <job>
        <title>{vacancy["vacancy_title"]}</title>
        <date>{vacancy["vacancy_creation_date"]}</date>
        <referencenumber>Workloupe-{vacancy["vacancy_id"]}</referencenumber>
        <url>https://workloupe.com/apply/{vacancy["vacancy_id"]}?source=postjobfree</url>
        <company>{vacancy["employer_name"]}</company>


        <location>{vacancy["location_full"]}</location>

        <description>{vacancy["clean_description"]}</description>

        <email><EMAIL></email>
        <cpc>0.00</cpc>
        <salary>{"Unknown" if not vacancy["salary_max"] or float(vacancy["salary_max"]) <= 10 else f"Up to {vacancy['salary_max']} {vacancy['salary_currency']} per month"}</salary>
    </job>"""

    xml_output += """
</source>"""

    return xml_output, 200, {"Content-Type": "application/xml"}

@app.route("/legal")
def legal():
    return render_template("legal.html")

@app.route("/all/himalayas.rss")
def xml_feed_himalayas():
    # Get active vacancies for employer that have Himalayas in job_portals and join with location details
    conn, cur = ats_safe_connect()

    # Get active vacancies with employer details
    cur.execute(
        """
        SELECT * FROM (SELECT v.*, e.employer_name, e.employer_logo_url, e.employer_description, e.employer_social_portals, e.employer_email, e.office_locations
        FROM feed_vacancy v
        INNER JOIN feed_employer e ON v.employer_id::text = e.employer_id::text
        WHERE v.vacancy_status = 'Active' AND v.job_portals LIKE '%Himalayas%' AND v.office_schedule = 'Fully Remote'
        ) x
        INNER JOIN
        (
        SELECT * FROM feed_officelocation
        ) y
        ON
        x.vacancy_city::text = y.city::text
        AND x.employer_id::text = y.employer_id::text
        --y.country::text = x.vacancy_country::text
        """
    )

    vacancies = [dict(zip([col[0] for col in cur.description], row)) for row in cur.fetchall()]

    cur.close()

    # Create XML feed
    xml_output = """<?xml version="1.0" encoding="utf-8"?>
<rss version="2.0">
    <source>
        <title>Workloupe.com Jobs Feed for Himalayas.app</title>
        <link>https://workloupe.com</link>
        <description>Remote job opportunities from Workloupe</description>

    <jobs>
    """

    for vacancy in vacancies:
        # Clean description
        clean_desc = clean_html_description(vacancy["vacancy_job_description"])

        # get location details
        if isinstance(vacancy["location_details"], dict):
            location_details = vacancy["location_details"]
        else:
            location_details = json.loads(vacancy["location_details"])


        # Format social links if available
        social_links = []
        if vacancy["employer_social_portals"]:
            for portal in str(vacancy["employer_social_portals"]).split(","):
                try:
                    platform, url = portal.strip().split(";")
                    social_links.append(f"<social platform='{platform}'><![CDATA[{url}]]></social>")
                except:
                    continue

        # Determine seniority level
        title_lower = vacancy["vacancy_title"].lower()
        if "senior" in title_lower or "sr." in title_lower:
            seniority = "Senior"
        elif any(level in title_lower for level in ["junior", "jr.", "entry", "intern"]):
            seniority = "Entry-Level"
        else:
            seniority = "Mid-Level"

        # Extract employer url from the employer mail
        company_url = vacancy["employer_email"].split("@")[1]

        # Office Location country unique
        office_locations = []
        for location in str(vacancy["office_locations"]).split("|"):
            try:
                country = location.split(",")[-1].strip()
                office_locations.append(country)
            except:
                continue

        xml_output += f"""
        <job>
            <job_title><![CDATA[{vacancy["vacancy_title"]}]]></job_title>
            <job_description><![CDATA[{clean_desc}]]></job_description>
            <country_requirements><![CDATA[{location_details["country"] or ""}]]></country_requirements>
            <application_link><![CDATA[https://workloupe.com/apply/{vacancy["vacancy_id"]}?source=himalayas]]></application_link>
            <company_name><![CDATA[{vacancy["employer_name"]}]]></company_name>
            <company_url><![CDATA[https://{company_url}]]></company_url>
            <expiration_date><![CDATA[{(vacancy["vacancy_creation_date"] + datetime.timedelta(days=60)).strftime("%a, %d %b %Y %H:%M:%S %z")}]]></expiration_date>
            <publication_date><![CDATA[{vacancy["vacancy_creation_date"].strftime("%a, %d %b %Y %H:%M:%S %z") if vacancy["vacancy_creation_date"] else ""}]]></publication_date>
            <seniority><![CDATA[{seniority}]]></seniority>
            <employment_type><![CDATA[{vacancy["work_schedule"] or "full-time"}]]></employment_type>
            <min_salary><![CDATA[{vacancy["salary_min"] or ""}]]></min_salary>
            <max_salary><![CDATA[{vacancy["salary_max"] or ""}]]></max_salary>
            <currency><![CDATA[{vacancy["salary_currency"] or ""}]]></currency>
            <company_about><![CDATA[{vacancy["employer_description"] or ""}]]></company_about>
            <company_logo><![CDATA[{vacancy["employer_logo_url"] or ""}]]></company_logo>
            <company_countries><![CDATA[{",".join(office_locations)}]]></company_countries>
            {"".join(social_links)}
        </job>"""

    xml_output += """
    </jobs>
    </source>
</rss>"""

    cur.close()
    return xml_output, 200, {"Content-Type": "application/xml"}


if __name__ == "__main__":
    app.run(host="0.0.0.0", debug=True, port=8888)
