# Use multi-stage build to reduce final image size
FROM python:3.11-alpine AS base

# Install system dependencies in a single layer
RUN apk update && apk add --no-cache \
    tesseract-ocr \
    poppler-utils \
    curl \
    bash \
    postgresql-dev \
    gcc \
    musl-dev \
    python3-dev \
    libffi-dev \
    jpeg-dev \
    zlib-dev

# Create non-root user for security
RUN addgroup -g 1001 appuser && adduser -D -u 1001 -G appuser appuser

# Set working directory
WORKDIR /flask-app

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip cache purge

# Copy application files
COPY --chown=appuser:appuser app.py .
#COPY --chown=appuser:appuser .env .
COPY --chown=appuser:appuser ./templates ./templates
COPY --chown=appuser:appuser ./static ./static
COPY --chown=appuser:appuser ./cv_storage ./cv_storage
COPY --chown=appuser:appuser ./custom_libs ./custom_libs
COPY --chown=appuser:appuser manage.py .
COPY --chown=appuser:appuser create_tables.py .

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Start application
CMD ["gunicorn", "--workers", "4", "--bind", "0.0.0.0:8080", "--timeout", "120", "--keep-alive", "2", "--max-requests", "1000", "--max-requests-jitter", "50", "app:app"]
