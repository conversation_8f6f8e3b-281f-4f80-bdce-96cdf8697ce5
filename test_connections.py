#!/usr/bin/env python3
"""
Test script to verify S3 and email connections are working properly.
"""

import os
import boto3
import redmail
from dotenv import load_dotenv

load_dotenv(override=True)

def test_s3_connection():
    """Test S3/DigitalOcean Spaces connection."""
    print("🔍 Testing S3/DigitalOcean Spaces connection...")
    
    try:
        # Get credentials
        aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        aws_region_name = os.getenv("AWS_REGION_NAME", "fra1")
        aws_endpoint_url = os.getenv("AWS_ENDPOINT_URL", "https://fra1.digitaloceanspaces.com")
        
        print(f"Access Key ID: {aws_access_key_id[:10]}..." if aws_access_key_id else "Access Key ID: Not found")
        print(f"Secret Key: {'*' * 20}" if aws_secret_access_key else "Secret Key: Not found")
        print(f"Region: {aws_region_name}")
        print(f"Endpoint: {aws_endpoint_url}")
        
        if not aws_access_key_id or not aws_secret_access_key:
            print("❌ AWS credentials not found in environment variables")
            return False
        
        # Initialize S3 client
        s3_session = boto3.session.Session()
        client = s3_session.client(
            "s3",
            region_name=aws_region_name,
            endpoint_url=aws_endpoint_url,
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
        )
        
        # Test connection by listing buckets
        response = client.list_buckets()
        print(f"✅ S3 connection successful! Found {len(response['Buckets'])} buckets:")
        for bucket in response['Buckets']:
            print(f"   - {bucket['Name']}")
        
        # Test specific bucket access
        try:
            objects = client.list_objects_v2(Bucket="applicants-cv-bucket", MaxKeys=5)
            print(f"✅ applicants-cv-bucket accessible. Contains {objects.get('KeyCount', 0)} objects")
        except Exception as bucket_error:
            print(f"⚠️  Warning: Cannot access applicants-cv-bucket: {str(bucket_error)}")
        
        return True
        
    except Exception as e:
        print(f"❌ S3 connection failed: {str(e)}")
        return False

def test_email_connection():
    """Test email connection."""
    print("\n📧 Testing email connection...")
    
    try:
        # Get email configuration
        smtp_host = os.getenv("SMTP_MAIL_HOST")
        smtp_port = int(os.getenv("SMTP_MAIL_PORT", 587))
        username = os.getenv("MAIL_USERNAME")
        password = os.getenv("MAIL_PASSWORD")
        
        print(f"SMTP Host: {smtp_host}")
        print(f"SMTP Port: {smtp_port}")
        print(f"Username: {username}")
        print(f"Password: {'*' * 10}" if password else "Password: Not found")
        
        if not all([smtp_host, username, password]):
            print("❌ Email configuration incomplete")
            return False
        
        # Test connection
        r_email = redmail.EmailSender(
            host=smtp_host,
            port=smtp_port,
            username=username,
            password=password,
        )
        
        # Try to connect
        r_email.connect()
        print("✅ Email connection successful!")
        
        # Close connection
        r_email.close()
        print("✅ Email connection closed properly")
        
        return True
        
    except Exception as e:
        print(f"❌ Email connection failed: {str(e)}")
        return False

def main():
    """Run all connection tests."""
    print("🚀 Starting connection tests...\n")
    
    s3_ok = test_s3_connection()
    email_ok = test_email_connection()
    
    print("\n" + "="*50)
    print("📊 Test Results Summary:")
    print(f"S3/DigitalOcean Spaces: {'✅ PASS' if s3_ok else '❌ FAIL'}")
    print(f"Email (SMTP): {'✅ PASS' if email_ok else '❌ FAIL'}")
    
    if s3_ok and email_ok:
        print("\n🎉 All connections are working properly!")
    else:
        print("\n⚠️  Some connections failed. Please check your environment variables.")
        print("\nRequired environment variables:")
        print("- AWS_ACCESS_KEY_ID")
        print("- AWS_SECRET_ACCESS_KEY") 
        print("- AWS_REGION_NAME (optional, defaults to 'fra1')")
        print("- AWS_ENDPOINT_URL (optional, defaults to DigitalOcean fra1)")
        print("- SMTP_MAIL_HOST")
        print("- SMTP_MAIL_PORT")
        print("- MAIL_USERNAME")
        print("- MAIL_PASSWORD")

if __name__ == "__main__":
    main()
