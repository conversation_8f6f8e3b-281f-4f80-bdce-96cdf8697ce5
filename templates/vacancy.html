{% extends "base.html" %} {% block title %} {{ vacancy.vacancy_title }} - Apply {% endblock %}
{% block content %}

<!-- Modern Job Header -->
<section class="job-apply-header">
  <div class="job-banner-container">
    {% if employer.employer_banner_url %}
    <div
      class="job-banner"
      style="background-image: url('{{ employer.employer_banner_url }}');"
    ></div>
    {% else %}
    <div class="job-banner job-banner-default"></div>
    {% endif %}
    <div class="job-banner-overlay"></div>
  </div>

  <div class="container">
    <div class="job-header-content less-limited-width-content">
      <div class="row align-items-center">
        <div class="col-lg-12 mx-2">
          <div class="job-header-info">
            <div class="company-logo-section">
              <div class="company-logo-container">
                {% if employer.employer_logo_url %}
                <img
                  src="{{ employer.employer_logo_url }}"
                  alt="{{ employer.employer_name }} logo"
                  class="company-logo-apply"
                  onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                />
                {% endif %}
                <div class="company-logo-fallback-apply" {% if employer.employer_logo_url %}style="display: none;"{% endif %}>
                  {{ (employer.employer_name or 'C')[0]|upper }}
                </div>
              </div>
              <div class="company-info">
                <h1 class="job-title-apply">{{ vacancy.vacancy_title }}</h1>
                <p class="company-name-apply">{{ employer.employer_name }}</p>
                <div class="job-meta-apply">
                  {% if vacancy.vacancy_city %}
                  <span class="meta-item">
                    <i class="bi bi-geo-alt"></i>
                    {{ vacancy.vacancy_city }}, {{ vacancy.vacancy_country }}
                  </span>
                  {% endif %}
                  {% if vacancy.office_schedule %}
                  <span class="meta-item">
                    <i class="bi bi-{{ 'house' if 'Remote' in (vacancy.office_schedule or '') else 'building' }}"></i>
                    {{ vacancy.office_schedule }}
                  </span>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
  </div>
</section>

<div class="container py-2 less-limited-width-content">
  <div class="row">
    <div class="col-lg-8 px-4">
        <div class="job-description-section">
          <h3 class="section-title-apply">
            <i class="bi bi-file-text me-2"></i>Job Description
          </h3>
          <div class="job-description-content">
            {{ vacancy.vacancy_job_description|safe }}
          </div>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="job-sidebar-apply">
          <!-- Action Buttons Section -->
          <div class="sidebar-section">
            <div class="action-buttons-sidebar">
              <button
                type="button"
                class="btn btn-hero-primary btn-lg apply-btn w-100 mb-3"
                data-bs-toggle="modal"
                data-bs-target="#applicationModal"
              >
                <i class="bi bi-send me-2"></i>Apply Now
              </button>
            </div>
          </div>

          <div class="sidebar-section">
            <h4 class="sidebar-title">
              <i class="bi bi-info-circle me-2"></i>Job Details
            </h4>
            <div class="job-details-list">
              {% if vacancy.vacancy_type %}
              <div class="detail-item-apply">
                <span class="detail-label-apply">Employment Type</span>
                <span class="detail-value-apply">{{ vacancy.vacancy_type }}</span>
              </div>
              {% endif %}
              {% if vacancy.office_schedule %}
              <div class="detail-item-apply">
                <span class="detail-label-apply">Work Schedule</span>
                <span class="detail-value-apply">{{ vacancy.office_schedule }}</span>
              </div>
              {% endif %}
              {% if vacancy.vacancy_country %}
              <div class="detail-item-apply">
                <span class="detail-label-apply">Location</span>
                <span class="detail-value-apply">
                  {% if vacancy.vacancy_city %}{{ vacancy.vacancy_city }}, {% endif %}{{ vacancy.vacancy_country }}
                </span>
              </div>
              {% endif %}
              {% if vacancy.salary_min and vacancy.salary_max %}
              <div class="detail-item-apply">
                <span class="detail-label-apply">Salary Range</span>
                <span class="detail-value-apply">
                  {{ vacancy.salary_currency }} {{ vacancy.salary_min }} - {{ vacancy.salary_max }}
                </span>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Key Advantages Section -->
          {% if all_tags[vacancy.vacancy_id] %}
          <div class="job-highlights-section">
            <h3 class="section-title-apply">
              <i class="bi bi-star me-2"></i>Key Advantages
            </h3>
            <div class="job-tags-apply">
              {% for tag in all_tags[vacancy.vacancy_id] %}
              <span class="job-tag-apply">
                {{ tag }}
              </span>
              {% endfor %}
            </div>
          </div>
          {% endif %}

          <div class="sidebar-section">
            <h4 class="sidebar-title">
              <i class="bi bi-building me-2"></i>About Company
            </h4>
            <div class="company-info-apply">
              <p class="company-description">
                Learn more about {{ employer.employer_name }} and their mission by visiting their company profile.
              </p>
              <a
                href="{{ url_for('employer', employer_id=vacancy.employer_id) }}"
                target="_blank"
                class="btn btn-outline-primary w-100"
              >
                <i class="bi bi-arrow-up-right me-2"></i>View Company Profile
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Application Modal -->
<div class="modal fade" id="applicationModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title h5">Quick Application</h2>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <form action="/submit" enctype="multipart/form-data" method="post">
          <div class="mb-3">
            <label class="form-label">Position ID</label>
            <input
              name="vac_id"
              value="{{vacancy.vacancy_id}}"
              class="form-control"
              readonly
              required
              style="display: none;"
            />
          </div>

          <div class="mb-3">
            <label class="form-label">Position Title</label>
            <input
              value="{{vacancy.vacancy_title}}"
              class="form-control"
              name="vac_title"
              disabled
              required
              style="display: none;"
            />
          </div>

          <input
              value="{{source}}"
              name="source"
              type="hidden"
              required
            />

          <div class="mb-3 row">
            <div class="col-6">
              <label class="form-label">First Name</label>
              <input
                name="fname"
                type="text"
                class="form-control"
                placeholder="John"
                required
              />
            </div>
            <div class="col-6">
              <label class="form-label">Last Name</label>
              <input
                name="lname"
                type="text"
                class="form-control"
                placeholder="Doe"
                required
              />
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">Email</label>
            <input
              name="email"
              type="email"
              class="form-control"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div class="mb-3">
            <label class="form-label">Phone</label>
            <input
              name="phone"
              type="tel"
              class="form-control"
              placeholder="+1234567890"
              pattern="^\+?[1-9]\d{1,14}$"
              required
            />
          </div>

          <div class="mb-3">
            <label class="form-label">Notice Period</label>
            <select name="notice" class="form-select" required>
              <option>Ready immediately</option>
              <option>2 weeks</option>
              <option>1 month</option>
              <option>2 months</option>
              <option>3 months</option>
              <option>More than 3 months</option>
            </select>
          </div>

          <div class="mb-4">
            <label class="form-label">Upload CV (PDF only)</label>
            <input
              name="cv"
              type="file"
              class="form-control"
              accept=".pdf"
              onchange="validateFileSize(this)"
              required
            />
          </div>

          <!-- GDPR Consent Section -->
          <div class="mb-4 p-3 bg-light rounded-3 border">
            <!-- Implicit Consent Notice -->
            <div class="mb-3">
              <p class="small text-muted mb-2">
                <i class="bi bi-info-circle me-1"></i>
                By submitting your CV, you agree to the processing of your data for recruitment purposes as described in our
                <a href="/legal" target="_blank" class="text-primary">Privacy Policy</a>.
              </p>
            </div>

            <!-- Explicit Consent Checkbox -->
            <div class="form-check mb-3">
              <input
                type="checkbox"
                class="form-check-input"
                id="futureOpportunitiesModal"
                name="consent_future_opportunities"
                value="true"
              />
              <label class="form-check-label small" for="futureOpportunitiesModal">
                <i class="bi bi-database me-1"></i>
                (Optional) I agree to have my CV and personal data stored for future job opportunities and to be contacted by employers. 
              </label>
            </div>

            <!-- AI Use Disclaimer -->
            <div class="small text-muted">
              <i class="bi bi-robot me-1"></i>
              <strong>AI Processing Notice:</strong> We use AI tools to assist in analyzing CVs and matching candidates.
              Final decisions are always made by human recruiters. No fully automated decisions are made.
              <a href="/legal" target="_blank" class="text-primary">Learn more</a>
            </div>
          </div>

          <div class="d-grid">
            <button type="submit" class="btn btn-primary btn-lg">
              Submit Application
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  function validateFileSize(input) {
    const file = input.files[0];
    if (file) {
      const fileSizeInMB = file.size / 1024 / 1024;
      if (fileSizeInMB > 3) {
        alert("File size must be less than 3MB");
        input.value = "";
      } else if (file.type !== "application/pdf") {
        alert("File must be a PDF");
        input.value = "";
      }
    }
  }

  // Add a small loading animation to the submit button
  document.addEventListener("DOMContentLoaded", function () {
    const submitButton = document.querySelector(".btn-primary");
    submitButton.addEventListener("click", function () {
      submitButton.innerHTML = "Submitting...";
    });
  });

</script>

<style>
  @media (max-width: 768px) {
    .employer-icon-md {
      max-width: 60px;
    }

    h1 {
      font-size: 1.5rem;
    }

    .modal-content {
      margin: 1rem;
    }

    .btn-lg {
      padding: 0.75rem 1rem;
      font-size: 1rem;
    }
  }
</style>

{% endblock %}
