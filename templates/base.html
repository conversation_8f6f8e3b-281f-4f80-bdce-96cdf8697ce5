<!DOCTYPE html>
<html lang="en">
  <head>
    <title>workloupe</title>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link
      rel="icon"
      type="image/png"
      href="{{ url_for('static', filename='favicon-96x96.png') }}"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/svg+xml"
      href="{{ url_for('static', filename='favicon.svg') }}"
    />
    <link
      rel="shortcut icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='apple-touch-icon.png') }}"
    />
    <meta name="apple-mobile-web-app-title" content="WorkLoupe" />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='site.webmanifest') }}"
    />

    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
      crossorigin="anonymous"
    />
    <script
      src="{{ url_for('static',filename='htmx.min.js') }}"
      type="text/javascript"
    ></script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="{{ url_for('static',filename='style.css') }}"
    />

    <!-- AOS (Animate On Scroll) Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
      crossorigin="anonymous"
    ></script>

    <!-- AOS JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JavaScript -->

    <script>
      // Cookie consent banner management
      document.addEventListener('DOMContentLoaded', function() {
        // Show banner if no consent decision made
        if (!localStorage.getItem('cookieConsent')) {
          document.getElementById('cookieConsent').style.display = 'block';
        }
      });

      function acceptCookies() {
        setCookieConsent(true);
        hideCookieBanner();
      }

      function rejectCookies() {
        setCookieConsent(false);
        hideCookieBanner();
      }

      function hideCookieBanner() {
        const banner = document.getElementById('cookieConsent');
        banner.style.animation = 'slideDown 0.3s ease-out';
        setTimeout(() => {
          banner.style.display = 'none';
        }, 300);
      }

      // Add slideDown animation
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideDown {
          from {
            transform: translateY(0);
            opacity: 1;
          }
          to {
            transform: translateY(100%);
            opacity: 0;
          }
        }
      `;
      document.head.appendChild(style);
    </script>

    <!-- Auto Dark Theme Script -->
    <script>
      // Automatic theme management based on system preference only
      const html = document.documentElement;

      // Apply theme based on system preference
      function applyTheme(isDark) {
        if (isDark) {
          html.setAttribute('data-theme', 'dark');
        } else {
          html.removeAttribute('data-theme');
        }
      }

      // Initialize theme based on system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      applyTheme(prefersDark);

      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        applyTheme(e.matches);
      });

      // Active page indication
      document.addEventListener('DOMContentLoaded', function() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.modern-nav-link[data-page]');

        navLinks.forEach(link => {
          const page = link.getAttribute('data-page');
          const href = link.getAttribute('href');

          // Check if current path matches the link
          if (currentPath === href ||
              (page === 'jobs' && currentPath.includes('/jobs')) ||
              (page === 'job-alerts' && currentPath.includes('/job-alerts')) ||
              (page === 'talent-pool' && currentPath.includes('/talent-pool')) ||
              (page === 'employers' && currentPath.includes('/employers')) ||
              (page === 'about' && currentPath.includes('/about'))) {
            link.classList.add('active-page');
          }
        });

        // Initialize AOS (Animate On Scroll)
        AOS.init({
          duration: 300,
          easing: 'ease-in-out',
          once: true,
          offset: 100
        });
      });
    </script>
  </head>

  <!-- Google tag (gtag.js) - Only load if consent given -->
  <script>
    // Cookie consent management
    function getCookieConsent() {
      return localStorage.getItem('cookieConsent') === 'accepted';
    }

    function setCookieConsent(accepted) {
      localStorage.setItem('cookieConsent', accepted ? 'accepted' : 'rejected');
      if (accepted) {
        loadGoogleAnalytics();
      }
    }

    function loadGoogleAnalytics() {
      // Load Google Analytics script
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.googletagmanager.com/gtag/js?id=G-3EWBWFSS82';
      document.head.appendChild(script);

      // Initialize Google Analytics
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-3EWBWFSS82');
    }

    // Load analytics if consent already given
    if (getCookieConsent()) {
      loadGoogleAnalytics();
    }
  </script>

  <body style="background-color: #fcfcfc">
    <nav
      class="navbar navbar-expand-md navbar-light modern-navbar sticky-top shadow-sm"
    >
      <div class="container-fluid less-limited-width-content py-3">
        <a class="navbar-brand modern-brand mx-4" href="/">
          <img
            src="{{ url_for('static',filename='logo.png') }}"
            alt=""
            width="162"
            height="28"
            class="d-inline-block align-text-top me-2 text-primary"
          />
        </a>
        <!-- Add data-bs-toggle and data-bs-target attributes to the button -->
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarScroll"
          aria-controls="navbarScroll"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <!-- Add the navbar-toggler-icon class for the hamburger icon -->
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarScroll">
          <ul class="navbar-nav ms-auto my-2 my-lg-0 navbar-nav-scroll">
            <li class="nav-item mx-2">
              <a class="nav-link modern-nav-link" href="/jobs" data-page="jobs">
                <i class="bi bi-briefcase me-1"></i>Jobs
              </a>
            </li>
            <!-- Job Alerts link hidden for a while, it's a bit broken -->
            <li class="nav-item mx-2" style="display: none;">
              <a class="nav-link modern-nav-link" href="/job-alerts" data-page="job-alerts">
                <i class="bi bi-bell me-1"></i>Job Alerts
              </a>
            </li>
            <li class="nav-item mx-2">
              <a class="nav-link modern-nav-link" href="/talent-pool" data-page="talent-pool">
                <i class="bi bi-people me-1"></i>Talent Pool
              </a>
            </li>
            <li class="nav-item mx-2">
              <a class="nav-link modern-nav-link" href="/employers" data-page="employers">
                <i class="bi bi-building me-1"></i>Employers
              </a>
            </li>
            <li class="nav-item mx-2">
              <a
                class="btn btn-primary modern-cta-btn"
                href="/employer-signup"
                ><i class="bi bi-plus-circle me-1"></i>Post Jobs</a
              >
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="container-fluid">
      <br />
      {% with messages = get_flashed_messages(with_categories=true) %} {% if
      messages %} {% for category, message in messages %}
      <div
        class="alert alert-{{ category }} alert-dismissible fade show"
        role="alert"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>
      {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock %}

      <br />
    </div>

    <footer class="py-3 my-4 space-from-top">
      <ul class="nav justify-content-center border-bottom pb-3 mb-3">
        <li class="nav-item">
          <a href="/" class="nav-link px-2 text-body-secondary text-primary"
            >Home</a
          >
        </li>
        <li class="nav-item">
          <a
            href="/employers"
            class="nav-link px-2 text-body-secondary text-primary"
            >Employers</a
          >
        </li>
        <li class="nav-item">
          <a href="/jobs" class="nav-link px-2 text-body-secondary text-primary"
            >Jobs</a
          >
        </li>
        <li class="nav-item">
          <a
            href="/about"
            class="nav-link px-2 text-body-secondary text-primary"
            >About Us</a
          >
        </li>
        <li class="nav-item">
          <a
            href="/legal"
            class="nav-link px-2 text-body-secondary text-primary"
            >Legal</a
          >
        </li>
      </ul>
      <p class="text-center text-body-secondary">© 2025 Canvider</p>
    </footer>

    <!-- Cookie Consent Banner -->
    <div id="cookieConsent" class="cookie-consent-banner" style="display: none;">
      <div class="cookie-consent-content">
        <div class="cookie-consent-text">
          <i class="bi bi-cookie me-2"></i>
          <span>On our website, we use cookies. 
          We use them to ensure the proper functioning of the site and, if you consent,
           for purposes such as analytics or marketing. We would like to ask for your
            permission to store cookies on your device. Optional cookies will not be activated
             until you enable them yourself. To learn more about how cookies work and how we process
              related personal data, please visit our Privacy Policy: </span>
          <a href="/legal" target="_blank" class="cookie-policy-link">Privacy Policy</a>
          <br>
          <span>We use analytical tools such as Google Analytics, which require cookies to verify website traffic. <span> 
          <a href="https://policies.google.com/" target="_blank"> Learn More </a>

        </div>
        <div class="cookie-consent-buttons">
          <button type="button" class="btn btn-outline-light btn-sm me-2" onclick="rejectCookies()">
            Reject
          </button>
          <button type="button" class="btn btn-primary btn-sm" onclick="acceptCookies()">
            Accept
          </button>
        </div>
      </div>
    </div>

    <style>
      .cookie-consent-banner {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(33, 37, 41, 0.95);
        backdrop-filter: blur(10px);
        color: white;
        padding: 1rem;
        z-index: 9999;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        animation: slideUp 0.3s ease-out;
      }

      .cookie-consent-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
      }

      .cookie-consent-text {
        flex: 1;
        min-width: 300px;
        font-size: 0.9rem;
      }

      .cookie-policy-link {
        color: #0d6efd;
        text-decoration: underline;
        margin-left: 0.5rem;
      }

      .cookie-consent-buttons {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
      }

      @keyframes slideUp {
        from {
          transform: translateY(100%);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }

      @media (max-width: 768px) {
        .cookie-consent-content {
          flex-direction: column;
          text-align: center;
        }

        .cookie-consent-text {
          min-width: auto;
        }
      }
    </style>
  </body>
</html>
